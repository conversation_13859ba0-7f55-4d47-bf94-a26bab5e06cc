# 宠物博客站群系统开发需求文档

## 📋 项目概述

### 项目目标
开发一个面向国际市场的宠物博客站群系统，专注于猫狗相关内容的知识分享，通过多语言、多域名的方式覆盖美国、德国、俄罗斯等目标市场，实现优秀的Google SEO表现和用户体验。

### 核心价值主张
- 专业的宠物知识分享平台
- 优秀的Google SEO表现
- 多语言、多域名独立运营
- 高效的内容管理和翻译工作流
- 灵活的广告管理系统

### 目标用户
- 宠物爱好者和养宠人群
- 寻求专业宠物知识的用户
- 不同语言地区的宠物社区

## 🛠 技术栈要求

### 前端技术栈
- **框架**: Astro（必须）
- **样式**: 推荐使用现代CSS框架，确保响应式设计
- **SEO优化**: 静态生成，优秀的Core Web Vitals表现
- **多语言策略**: 每种语言独立模板，非i18n方案

### 后端技术栈
- **语言**: 请根据最佳实践选择（Node.js/Python/Go等）
- **框架**: 选择成熟稳定的框架
- **API设计**: RESTful API，支持OpenAI接口规范
- **文件上传**: 支持图片上传和富文本编辑器

### 数据库
- **类型**: MySQL（必须）
- **连接信息**:
  - IP: ************
  - 数据库名: bengtai
  - 账号: bengtai
  - 密码: weizhen258

### 第三方服务
- **翻译API**: 
  - 地址: https://ai.wanderintree.top
  - 密钥: sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
  - 模型: gemini-2.5-pro
- **广告系统**: Google Ads
- **统计分析**: Google Analytics

## 🎯 详细功能需求

### 1. 前端功能需求

#### 1.1 页面结构要求
**必须包含的页面类型**：
- 首页（Homepage）
- 文章详情页（Article Detail）
- 分类页面（Category Pages）
- 搜索结果页（Search Results）
- 关于我们页面（About Us）
- 隐私政策页面（Privacy Policy）
- 联系我们页面（Contact Us）

#### 1.2 SEO优化要求
**技术SEO**：
- 静态HTML生成，确保爬虫友好
- 完整的meta标签（title, description, keywords）
- 结构化数据（JSON-LD格式）
- 自动生成XML sitemap
- robots.txt文件
- 优秀的Core Web Vitals表现
- 移动端友好设计
- 快速加载速度（LCP < 2.5s）

**内容SEO**：
- 本地化URL结构（使用目标语言的URL slug）
- 面包屑导航
- 内部链接优化
- 图片alt标签和优化
- 语义化HTML结构
- H1-H6标签层次结构
- 关键词密度优化（2-3%）
- 长尾关键词布局

#### 1.3 用户交互功能
- **搜索功能**: 全站搜索，支持关键词和分类筛选
- **评论系统**:
  - 多层嵌套评论（最多3层）
  - 用户只需填写用户名和邮箱
  - 所有评论需要后台审核才能显示
  - 反垃圾评论机制
- **分类导航**: 清晰的分类结构，最多两级分类
- **相关文章推荐**: 基于分类和标签的智能推荐

#### 1.4 分类结构要求
**顶级分类（一级分类）**：
- 猫（Cats）
- 狗（Dogs）

**二级分类结构**：

**猫分类（Cats）**：
- 猫咪健康（Cat Health）
- 猫咪训练（Cat Training）
- 猫咪美容（Cat Grooming）
- 猫咪行为（Cat Behavior）
- 猫咪营养（Cat Nutrition）
- 猫咪品种（Cat Breeds）

**狗分类（Dogs）**：
- 狗狗健康（Dog Health）
- 狗狗训练（Dog Training）
- 狗狗美容（Dog Grooming）
- 狗狗行为（Dog Behavior）
- 狗狗营养（Dog Nutrition）
- 狗狗品种（Dog Breeds）

**分类命名要求**：
- 每种语言的分类名称必须完全本地化
- URL slug使用目标语言的关键词
- 分类描述需要针对SEO优化
- 支持后台自定义分类顺序

#### 1.5 URL结构和SEO最佳实践

**URL结构规范**（基于Google SEO最佳实践）：

**首页URL**：
- 英语：`https://example.com/`
- 德语：`https://example.de/`
- 俄语：`https://example.ru/`

**分类页URL**：
- 英语：`https://example.com/cats/` 或 `https://example.com/dogs/`
- 德语：`https://example.de/katzen/` 或 `https://example.de/hunde/`
- 俄语：`https://example.ru/кошки/` 或 `https://example.ru/собаки/`

**二级分类URL**：
- 英语：`https://example.com/cats/health/`
- 德语：`https://example.de/katzen/gesundheit/`
- 俄语：`https://example.ru/кошки/здоровье/`

**文章详情页URL**：
- 英语：`https://example.com/cats/health/article-title-in-english/`
- 德语：`https://example.de/katzen/gesundheit/artikel-titel-auf-deutsch/`
- 俄语：`https://example.ru/кошки/здоровье/заголовок-статьи-на-русском/`

**搜索页URL**：
- 英语：`https://example.com/search/?q=keyword`
- 德语：`https://example.de/suche/?q=keyword`
- 俄语：`https://example.ru/поиск/?q=keyword`

**URL优化要求**：
- 使用连字符（-）分隔单词，不使用下划线
- URL长度控制在60个字符以内
- 使用小写字母
- 避免特殊字符和参数
- 包含目标关键词
- 结构层次清晰，不超过3级深度
- 支持自动生成canonical标签

#### 1.6 多语言架构
- **独立模板策略**: 每种语言一个完整的前端模板
- **域名绑定**: 每个域名对应一种语言
- **可扩展性**: 支持后期添加新语言模板
- **本地化内容**: 分类名称、UI文本等完全本地化

### 2. 后台管理系统需求

#### 2.1 内容管理功能
**文章管理**：
- 富文本编辑器（支持图片粘贴上传）
- 文章状态管理（草稿、已发布、已下线）
- SEO元数据设置（标题、描述、关键词）
- 分类和标签管理
- 发布时间设置
- 文章预览功能

**翻译工作流**：
- 中文原文输入
- 一键翻译功能（调用AI API）
- 翻译结果保存为草稿
- 人工校对界面
- 批量发布到指定语言站点

#### 2.2 多语言站点管理
- **域名绑定**: 域名与语言的绑定管理
- **站点配置**: 每个语言站点的独立配置
- **内容分发**: 翻译后内容到指定语言站点的分发

#### 2.3 评论管理
- 评论审核队列
- 批量审核操作
- 垃圾评论过滤
- 评论回复功能

#### 2.4 广告和统计管理
**广告管理**：
- 每个语言站点独立的Google Ads设置
- 广告位置管理
- 广告开关控制（关闭时前端不显示任何广告代码）
- 广告代码管理

**统计管理**：
- 每个语言站点独立的Google Analytics设置
- 统计代码管理
- 基础数据展示

#### 2.5 系统设置
- **AI翻译配置**: API地址、密钥、模型设置
- **管理员账户管理**: 单一管理员账户
- **系统日志**: 操作日志和错误日志
- **数据库备份**: 手动备份功能

### 3. API接口需求

#### 3.1 前端API
- 文章列表接口（支持分页、分类筛选）
- 文章详情接口
- 搜索接口
- 评论提交接口
- 评论列表接口

#### 3.2 后台API
- 管理员认证接口
- 文章CRUD接口
- 翻译接口（对接AI服务）
- 评论管理接口
- 站点配置接口
- 文件上传接口

## 🏗 架构设计要求

### 1. 系统架构
- **前后端分离**: 前端静态生成，后端提供API
- **多域名支持**: 通过域名识别语言模板
- **可扩展性**: 支持新增语言和功能模块
- **性能优化**: 缓存策略、CDN支持

### 2. 数据库设计
**核心表结构**：
- 文章表（支持多语言版本）
- 分类表（多语言分类名称，支持两级分类）
- 评论表（支持嵌套结构）
- 站点配置表（域名绑定、广告设置等）
- 管理员表
- 翻译记录表

**分类表设计要求**：
- 支持父子级关系（最多两级）
- 每种语言的分类名称和URL slug
- 分类描述和SEO元数据
- 分类排序权重
- 分类状态（启用/禁用）

**文章表设计要求**：
- 支持多语言版本关联
- SEO元数据字段（title, description, keywords）
- URL slug字段（本地化）
- 发布状态和时间
- 分类关联（支持多分类）

### 3. 文件存储
- **图片存储**: 本地服务器存储
- **路径规范**: 按语言和日期组织目录结构
- **图片优化**: 自动压缩和格式转换

## 📊 非功能性需求

### 1. 性能要求
- **页面加载速度**: 首屏加载时间 < 2.5秒
- **并发处理**: 支持1万日活用户，后期可扩展
- **数据库性能**: 查询响应时间 < 100ms
- **图片加载**: 懒加载和WebP格式支持

### 2. 安全要求
- **数据安全**: SQL注入防护、XSS防护
- **文件上传安全**: 文件类型验证、大小限制
- **管理员认证**: 安全的登录机制
- **评论安全**: 反垃圾评论和内容过滤

### 3. SEO要求
- **技术SEO**: 完整的meta标签、结构化数据
- **内容SEO**: 本地化URL、内链优化
- **性能SEO**: Core Web Vitals优化
- **移动SEO**: 响应式设计、移动友好

## 🚀 开发环境和部署

### 1. 开发环境
- **本地开发**: Mac系统
- **数据库**: 使用远程MySQL数据库
- **域名测试**: 本地hosts文件配置多域名测试
- **开发服务器**: 支持多域名本地访问

### 2. 部署环境
- **服务器**: Linux VPS + 宝塔面板
- **域名**: 顶级域名（非子域名）
- **SSL证书**: 自动配置HTTPS
- **静态资源**: 本地存储

## 📚 文档输出要求

### 1. 必须生成的文档
**项目文档**：
- 项目概述和架构说明
- 技术栈选择理由
- 数据库设计文档
- API接口文档
- 前端页面设计规范
- SEO优化指南

**开发文档**：
- 详细的开发步骤（不少于60步）
- 每个步骤的具体任务和验收标准
- 前端、后端、API的详细开发指南
- 测试计划和测试用例
- 部署指南

**运维文档**：
- 服务器配置指南
- 域名配置说明
- 数据库维护指南
- 故障排查手册

### 2. 文档质量要求
- **详细性**: 每个功能点都有详细说明
- **可操作性**: 步骤清晰，可直接执行
- **完整性**: 覆盖开发、测试、部署全流程
- **AI友好**: 适合AI理解和执行的格式

## 🎯 开发步骤要求

### 1. 步骤拆分原则
- **细粒度**: 每个步骤任务量适中，避免超出AI上下文限制
- **逻辑性**: 步骤间有清晰的依赖关系
- **可验证**: 每个步骤都有明确的完成标准
- **最少60步**: 确保充分的细分

### 2. 步骤内容要求
**每个步骤必须包含**：
- 步骤名称和编号
- 详细的任务描述
- 具体的实现要求
- 验收标准
- 相关文件和代码位置
- 测试方法

### 3. 开发阶段划分
1. **项目初始化阶段**（约10步）
2. **数据库设计和搭建阶段**（约8步）
3. **后端API开发阶段**（约15步）
4. **前端模板开发阶段**（约12步）
5. **多语言系统开发阶段**（约8步）
6. **后台管理系统开发阶段**（约10步）
7. **集成测试阶段**（约5步）
8. **部署和优化阶段**（约5步）

## ✅ 成功标准

### 1. 功能完整性
- 所有需求功能正常运行
- 多语言站点正确切换
- 翻译工作流顺畅
- 评论系统正常工作
- 广告管理功能完善

### 2. 性能指标
- 页面加载速度达标
- SEO指标优秀
- 数据库查询性能良好
- 系统稳定性高

### 3. 可维护性
- 代码结构清晰
- 文档完整详细
- 易于扩展新功能
- 便于添加新语言

## 🔧 技术实现建议

### 1. 域名识别方案
建议使用反向代理或应用层路由的方式，根据请求的域名自动识别并返回对应语言的模板内容。

### 2. 新语言添加流程
1. 复制现有语言模板
2. 翻译所有静态文本
3. 更新数据库配置
4. 配置域名绑定
5. 测试和上线

### 3. 本地开发多域名测试
通过修改hosts文件和配置开发服务器，实现本地多域名测试环境。

---

**重要提醒**: 请根据以上详细需求，生成完整的开发文档和步骤指南。所有文档都应该保存在本地的docs文件夹中，确保内容详细、结构清晰、便于AI理解和执行。如果对任何需求有疑问，请及时提出，我们需要确保需求理解完全准确后再开始文档制作。
